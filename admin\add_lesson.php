<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $subject_id = $_POST['subject_id'];
    $title = $_POST['title'];
    $description = $_POST['description'];
    $lesson_number = $_POST['lesson_number'];
    $is_free = isset($_POST['is_free']) ? 1 : 0;
    $sort_order = $_POST['sort_order'];
    
    try {
        $stmt = $db->prepare("INSERT INTO lessons (subject_id, title, description, lesson_number, is_free, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$subject_id, $title, $description, $lesson_number, $is_free, $sort_order]);
        
        $lesson_id = $db->lastInsertId();
        $success = "تم إضافة الدرس بنجاح!";
        
        // Redirect to lesson content page
        header("Location: lesson_content.php?lesson_id=$lesson_id");
        exit;
        
    } catch (Exception $e) {
        $error = "خطأ في إضافة الدرس: " . $e->getMessage();
    }
}

// Get subjects
$subjects_stmt = $db->prepare("SELECT * FROM curriculum_subjects WHERE is_active = 1 ORDER BY name");
$subjects_stmt->execute();
$subjects = $subjects_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة درس جديد - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>إضافة درس جديد</h1>
                <p>أضف درساً جديداً إلى أحد الأقسام الدراسية</p>
            </div>
                        <div class="header-actions">
                            <a href="lessons.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i>
                                العودة للدروس
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- Quick Add Form -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3><i class="fas fa-rocket"></i> إضافة سريعة</h3>
                        <p>املأ البيانات الأساسية للدرس وابدأ بإضافة المحتوى</p>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="admin-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="subject_id">القسم الدراسي</label>
                                    <select name="subject_id" id="subject_id" class="form-control" required>
                                        <option value="">اختر القسم</option>
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo $subject['id']; ?>" 
                                                    data-color="<?php echo $subject['color']; ?>"
                                                    data-icon="<?php echo $subject['icon']; ?>">
                                                <?php echo htmlspecialchars($subject['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="lesson_number">رقم الدرس</label>
                                    <input type="number" name="lesson_number" id="lesson_number" class="form-control" min="1" required>
                                    <small class="form-text">رقم الدرس في التسلسل</small>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="title">عنوان الدرس</label>
                                <input type="text" name="title" id="title" class="form-control" required 
                                       placeholder="مثال: مقدمة في النحو العربي">
                            </div>

                            <div class="form-group">
                                <label for="description">وصف الدرس (اختياري)</label>
                                <textarea name="description" id="description" class="form-control" rows="3" 
                                          placeholder="وصف مختصر عن محتوى الدرس وأهدافه"></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="sort_order">ترتيب العرض</label>
                                    <input type="number" name="sort_order" id="sort_order" class="form-control" min="0" value="0">
                                    <small class="form-text">ترتيب الدرس في القائمة (0 = الأول)</small>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="is_free" id="is_free">
                                        <span class="checkmark"></span>
                                        درس مجاني
                                    </label>
                                    <small class="form-text">يمكن للطلاب الوصول إليه بدون اشتراك</small>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-large">
                                    <i class="fas fa-save"></i>
                                    إضافة الدرس والانتقال للمحتوى
                                </button>
                                <a href="lessons.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Tips -->
                <div class="tips-card">
                    <div class="tips-header">
                        <h3><i class="fas fa-lightbulb"></i> نصائح سريعة</h3>
                    </div>
                    <div class="tips-body">
                        <div class="tip-item">
                            <i class="fas fa-check-circle"></i>
                            <span>اختر عنواناً واضحاً ومفهوماً للدرس</span>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-check-circle"></i>
                            <span>رتب الدروس بشكل منطقي حسب التسلسل التعليمي</span>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-check-circle"></i>
                            <span>اجعل الدروس الأساسية مجانية لجذب الطلاب</span>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-check-circle"></i>
                            <span>بعد إضافة الدرس، ستنتقل مباشرة لإضافة المحتوى</span>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .admin-container {
            display: flex;
            flex: 1;
        }

        .admin-main {
            flex: 1;
            padding: 20px;
            margin-left: 280px;
        }

        .admin-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-left: 5px solid #28a745;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-info h1 {
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 28px;
            font-weight: 700;
        }

        .page-info p {
            color: #6c757d;
            margin: 0;
            font-size: 16px;
        }

        .admin-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 25px 30px;
        }

        .card-header h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
        }

        .card-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .card-body {
            padding: 30px;
        }

        .admin-form {
            max-width: 800px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .form-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            font-weight: 600;
            color: #2c3e50;
        }

        .checkbox-label input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #28a745;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .tips-card {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid #ffeaa7;
        }

        .tips-header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 20px 30px;
        }

        .tips-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .tips-body {
            padding: 25px 30px;
        }

        .tip-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
            color: #856404;
        }

        .tip-item:last-child {
            margin-bottom: 0;
        }

        .tip-item i {
            color: #28a745;
            font-size: 16px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .admin-main {
                margin-left: 0;
                padding: 10px;
            }

            .header-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>

    <script>
        // Auto-suggest lesson number based on selected subject
        document.getElementById('subject_id').addEventListener('change', function() {
            const subjectId = this.value;
            if (subjectId) {
                // Here you could make an AJAX call to get the next lesson number
                // For now, we'll just suggest a default
                document.getElementById('lesson_number').value = 1;
            }
        });

        // Auto-focus on title after selecting subject
        document.getElementById('subject_id').addEventListener('change', function() {
            if (this.value) {
                setTimeout(() => {
                    document.getElementById('title').focus();
                }, 100);
            }
        });
    </script>

    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
