<?php
// تشخيص مشاكل الموقع
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>تشخيص مشاكل الموقع</h1>";

// 1. فحص إعدادات PHP
echo "<h2>1. إعدادات PHP</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";

// 2. فحص الملفات المطلوبة
echo "<h2>2. فحص الملفات المطلوبة</h2>";
$required_files = [
    'config/config.php',
    'includes/database.php',
    'DB/all-sheet.mysql',
    'includes/header.php',
    'includes/sidebar.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

// 3. فحص الاتصال بقاعدة البيانات
echo "<h2>3. فحص الاتصال بقاعدة البيانات</h2>";
try {
    require_once 'config/config.php';
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // فحص الجداول المطلوبة
    $tables = ['users', 'curriculum_subjects', 'notifications'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "✅ جدول $table موجود ($count سجل)<br>";
        } catch (Exception $e) {
            echo "❌ جدول $table غير موجود أو به مشكلة: " . $e->getMessage() . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
}

// 4. فحص الصلاحيات
echo "<h2>4. فحص الصلاحيات</h2>";
$directories = ['uploads', 'uploads/courses', 'uploads/videos', 'uploads/lessons'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ مجلد $dir قابل للكتابة<br>";
        } else {
            echo "⚠️ مجلد $dir غير قابل للكتابة<br>";
        }
    } else {
        echo "❌ مجلد $dir غير موجود<br>";
    }
}

// 5. فحص الثوابت المطلوبة
echo "<h2>5. فحص الثوابت المطلوبة</h2>";
$constants = ['SITE_URL', 'SITE_NAME', 'DB_HOST', 'DB_NAME', 'DB_USER'];
foreach ($constants as $constant) {
    if (defined($constant)) {
        echo "✅ $constant = " . constant($constant) . "<br>";
    } else {
        echo "❌ $constant غير محدد<br>";
    }
}

// 6. فحص الجلسة
echo "<h2>6. فحص الجلسة</h2>";
if (session_status() == PHP_SESSION_ACTIVE) {
    echo "✅ الجلسة نشطة<br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "❌ الجلسة غير نشطة<br>";
}

// 7. فحص ملف curriculum.php
echo "<h2>7. فحص ملف curriculum.php</h2>";
if (file_exists('page/curriculum.php')) {
    echo "✅ ملف curriculum.php موجود<br>";
    
    // فحص بناء الجملة
    $content = file_get_contents('page/curriculum.php');
    if (strpos($content, '<?php') === 0) {
        echo "✅ الملف يبدأ بـ PHP tag صحيح<br>";
    } else {
        echo "❌ الملف لا يبدأ بـ PHP tag صحيح<br>";
    }
    
    // فحص الأخطاء النحوية
    $output = [];
    $return_var = 0;
    exec("php -l page/curriculum.php 2>&1", $output, $return_var);
    if ($return_var === 0) {
        echo "✅ لا توجد أخطاء نحوية في curriculum.php<br>";
    } else {
        echo "❌ أخطاء نحوية في curriculum.php:<br>";
        foreach ($output as $line) {
            echo $line . "<br>";
        }
    }
} else {
    echo "❌ ملف curriculum.php غير موجود<br>";
}

echo "<h2>8. معلومات الخادم</h2>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";

// 9. فحص ملفات CSS و JS
echo "<h2>9. فحص الملفات الثابتة</h2>";
$static_files = [
    'css/styles.css',
    'css/enhanced-ui.css',
    'css/loading-system.css',
    'js/dashboard.js',
    'img/logo.png'
];

foreach ($static_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود (" . filesize($file) . " بايت)<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

// 10. فحص إعدادات الخادم
echo "<h2>10. إعدادات الخادم</h2>";
echo "Allow URL fopen: " . (ini_get('allow_url_fopen') ? 'مفعل' : 'معطل') . "<br>";
echo "File uploads: " . (ini_get('file_uploads') ? 'مفعل' : 'معطل') . "<br>";
echo "Session save path: " . session_save_path() . "<br>";
echo "Temp directory: " . sys_get_temp_dir() . "<br>";

// 11. اختبار كتابة ملف
echo "<h2>11. اختبار كتابة الملفات</h2>";
$test_file = 'test_write.txt';
if (file_put_contents($test_file, 'test')) {
    echo "✅ يمكن كتابة الملفات<br>";
    unlink($test_file);
} else {
    echo "❌ لا يمكن كتابة الملفات<br>";
}

echo "<hr>";
echo "<h2>الحلول المقترحة:</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>إذا كان الموقع لا يعمل:</h3>";
echo "<ol>";
echo "<li>تأكد من رفع جميع الملفات بشكل صحيح</li>";
echo "<li>تحقق من إعدادات قاعدة البيانات في config/config.php</li>";
echo "<li>تأكد من أن SITE_URL يشير للدومين الصحيح</li>";
echo "<li>تحقق من صلاحيات المجلدات (755 للمجلدات، 644 للملفات)</li>";
echo "<li>راجع سجل الأخطاء في لوحة تحكم الاستضافة</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><strong>روابط الاختبار:</strong></p>";
echo "<ul>";
echo "<li><a href='test.php'>اختبار بسيط</a></li>";
echo "<li><a href='page/curriculum_simple.php'>اختبار صفحة المنهج المبسطة</a></li>";
echo "<li><a href='page/curriculum.php'>صفحة المنهج الأصلية</a></li>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='login.php'>صفحة تسجيل الدخول</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p style='color: #666; font-size: 0.9em;'>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</p>";
?>
