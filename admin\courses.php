<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$courseManager = new CourseManager();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $courseData = [
                    'title' => $_POST['title'],
                    'subject' => $_POST['subject'],
                    'description' => $_POST['description'],
                    'features' => $_POST['features'],
                    'price' => floatval($_POST['price']),
                    'discount_percentage' => floatval($_POST['discount_percentage'] ?? 0),
                    'education_level' => $_POST['education_level'],
                    'education_type' => $_POST['education_type'],
                    'grade' => $_POST['grade'],
                    'specialization' => $_POST['specialization'],
                    'created_by' => $_SESSION['admin_id']
                ];

                // Handle main image upload
                if (!empty($_FILES['main_image']['name'])) {
                    $uploadResult = $courseManager->uploadCourseImage($_FILES['main_image']);
                    if ($uploadResult['success']) {
                        $courseData['main_image'] = $uploadResult['filename'];
                    } else {
                        $message = $uploadResult['message'];
                        $messageType = 'error';
                        break;
                    }
                }

                // Handle modal images upload
                $modalImages = [];
                if (!empty($_FILES['modal_images']['name'][0])) {
                    foreach ($_FILES['modal_images']['name'] as $key => $name) {
                        if (!empty($name)) {
                            $file = [
                                'name' => $_FILES['modal_images']['name'][$key],
                                'type' => $_FILES['modal_images']['type'][$key],
                                'tmp_name' => $_FILES['modal_images']['tmp_name'][$key],
                                'size' => $_FILES['modal_images']['size'][$key]
                            ];
                            $uploadResult = $courseManager->uploadCourseImage($file);
                            if ($uploadResult['success']) {
                                $modalImages[] = $uploadResult['filename'];
                            }
                        }
                    }
                }
                $courseData['modal_images'] = !empty($modalImages) ? json_encode($modalImages) : null;

                $courseId = $courseManager->createCourse($courseData);
                if ($courseId) {
                    $message = 'تم إنشاء الكورس بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'فشل في إنشاء الكورس';
                    $messageType = 'error';
                }
                break;

            case 'delete':
                if (isset($_POST['course_id'])) {
                    if ($courseManager->deleteCourse($_POST['course_id'])) {
                        $message = 'تم حذف الكورس بنجاح';
                        $messageType = 'success';
                    } else {
                        $message = 'فشل في حذف الكورس';
                        $messageType = 'error';
                    }
                }
                break;
        }
    }
}

// Get all courses
$courses = $courseManager->getAllCourses();
$courseStats = $courseManager->getCourseStatistics();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكورسات - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>إدارة الكورسات</h1>
                <p>إنشاء وإدارة الكورسات والدورات التدريبية</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Course Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($courses); ?></div>
                    <div class="stat-label">إجمالي الكورسات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo array_sum(array_column($courseStats, 'total_subscribers')); ?></div>
                    <div class="stat-label">إجمالي المشتركين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo array_sum(array_column($courseStats, 'active_subscribers')); ?></div>
                    <div class="stat-label">المشتركين النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo array_sum(array_column($courseStats, 'pending_subscribers')); ?></div>
                    <div class="stat-label">طلبات معلقة</div>
                </div>
            </div>

            <!-- Add New Course Button -->
            <div class="content-card">
                <div class="card-header">
                    <h2>إضافة كورس جديد</h2>
                    <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="toggleCourseForm()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        إضافة كورس جديد
                    </button>
                </div>

                <!-- Course Creation Form -->
                <div id="courseForm" class="course-form" style="display: none;">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="create">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="title">عنوان الكورس *</label>
                                <input type="text" id="title" name="title" required>
                            </div>

                            <div class="form-group">
                                <label for="subject">المادة *</label>
                                <input type="text" id="subject" name="subject" required>
                            </div>

                            <div class="form-group">
                                <label for="price">السعر *</label>
                                <input type="number" id="price" name="price" step="0.01" min="0" required>
                            </div>

                            <div class="form-group">
                                <label for="discount_percentage">نسبة الخصم (%)</label>
                                <input type="number" id="discount_percentage" name="discount_percentage" step="0.01" min="0" max="100" value="0">
                            </div>

                            <div class="form-group">
                                <label for="education_level">المرحلة التعليمية</label>
                                <select id="education_level" name="education_level">
                                    <option value="all">جميع المراحل</option>
                                    <option value="primary">ابتدائي</option>
                                    <option value="preparatory">إعدادي</option>
                                    <option value="secondary">ثانوي</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="education_type">نوع التعليم</label>
                                <select id="education_type" name="education_type">
                                    <option value="all">جميع الأنواع</option>
                                    <option value="azhari">أزهري</option>
                                    <option value="general">عام</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="grade">الصف</label>
                                <select id="grade" name="grade">
                                    <option value="all">جميع الصفوف</option>
                                    <option value="1">الأول</option>
                                    <option value="2">الثاني</option>
                                    <option value="3">الثالث</option>
                                    <option value="4">الرابع</option>
                                    <option value="5">الخامس</option>
                                    <option value="6">السادس</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="specialization">التخصص</label>
                                <select id="specialization" name="specialization">
                                    <option value="all">جميع التخصصات</option>
                                    <option value="scientific">علمي</option>
                                    <option value="literary">أدبي</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">وصف الكورس *</label>
                            <textarea id="description" name="description" rows="4" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="features">مميزات الكورس</label>
                            <textarea id="features" name="features" rows="3" placeholder="اكتب كل ميزة في سطر منفصل"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="main_image">الصورة الرئيسية</label>
                            <input type="file" id="main_image" name="main_image" accept="image/*">
                        </div>

                        <div class="form-group">
                            <label for="modal_images">صور إضافية للمودال</label>
                            <input type="file" id="modal_images" name="modal_images[]" accept="image/*" multiple>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary" onclick="openModal('addModal')">إنشاء الكورس</button>
                            <button type="button" class="btn btn-secondary" onclick="toggleCourseForm()">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Courses List -->
            <div class="content-card">
                <h2>الكورسات الحالية</h2>
                <?php if (!empty($courses)): ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>المادة</th>
                                    <th>السعر</th>
                                    <th>المشتركين</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($courses as $course): ?>
                                    <?php
                                    $stats = array_filter($courseStats, function($stat) use ($course) {
                                        return $stat['course_id'] == $course['id'];
                                    });
                                    $currentCourseStats = !empty($stats) ? array_values($stats)[0] : null;
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($course['title']); ?></td>
                                        <td><?php echo htmlspecialchars($course['subject']); ?></td>
                                        <td>
                                            <?php if ($course['discount_percentage'] > 0): ?>
                                                <span class="original-price"><?php echo number_format($course['price'], 2); ?> جنيه</span>
                                                <span class="discounted-price"><?php echo number_format($course['discounted_price'], 2); ?> جنيه</span>
                                            <?php else: ?>
                                                <?php echo number_format($course['price'], 2); ?> جنيه
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $currentCourseStats ? $currentCourseStats['total_subscribers'] : 0; ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($course['created_at'])); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="course_content.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-primary">المحتوى</a>
                                                <a href="course_subscribers.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-info">المشتركين</a>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الكورس؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p style="text-align: center; color: #666; padding: 40px;">لا توجد كورسات حالياً</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function toggleCourseForm() {
            const form = document.getElementById('courseForm');
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
