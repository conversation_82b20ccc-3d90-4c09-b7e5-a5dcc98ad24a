# إعدادات Apache للموقع
RewriteEngine On

# تفعيل عرض الأخطاء (للتطوير فقط)
php_flag display_errors On
php_flag display_startup_errors On
php_value error_reporting "E_ALL"

# إعدادات PHP
php_value memory_limit 256M
php_value max_execution_time 300
php_value upload_max_filesize 50M
php_value post_max_size 50M

# إعدادات الترميز
AddDefaultCharset UTF-8

# حماية الملفات الحساسة
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

# إعادة توجيه الأخطاء
ErrorDocument 404 /error/404.php
ErrorDocument 403 /error/403.php
ErrorDocument 500 /error/500.php

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# منع الوصول للمجلدات الحساسة
RedirectMatch 403 ^/\.git
RedirectMatch 403 ^/config/
RedirectMatch 403 ^/includes/
RedirectMatch 403 ^/DB/

# إعادة كتابة URLs (اختياري)
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^([^/]+)/?$ page/$1.php [L,QSA]
