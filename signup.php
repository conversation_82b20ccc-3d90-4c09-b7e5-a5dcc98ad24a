<?php
require_once 'config/config.php';
require_once 'includes/database.php';
require_once 'includes/notification_helper.php';

$error = '';
$success = '';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/page/dashboard.php');
    exit;
}

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    if ($input && isset($input['action']) && $input['action'] === 'register') {
        try {
            $userManager = new UserManager();

            // Validate all required fields
            $requiredFields = [
                'username', 'email', 'password', 'first_name', 'second_name',
                'third_name', 'fourth_name', 'gender', 'birth_date',
                'personal_phone', 'father_phone', 'mother_phone',
                'education_level', 'education_type', 'grade'
            ];

            $errors = [];

            foreach ($requiredFields as $field) {
                if (empty($input[$field])) {
                    $errors[] = "الحقل {$field} مطلوب";
                }
            }

            // Additional validations
            if (!$errors) {
                // Check username uniqueness
                if ($userManager->usernameExists($input['username'])) {
                    $errors[] = ERROR_MESSAGES['username_exists'];
                }

                // Check email uniqueness
                if ($userManager->emailExists($input['email'])) {
                    $errors[] = ERROR_MESSAGES['email_exists'];
                }

                // Validate email format
                if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                    $errors[] = ERROR_MESSAGES['invalid_email'];
                }

                // Validate password strength
                $passwordErrors = $userManager->validatePassword($input['password']);
                if ($passwordErrors) {
                    $errors = array_merge($errors, $passwordErrors);
                }

                // Validate Arabic names
                $nameFields = ['first_name', 'second_name', 'third_name', 'fourth_name'];
                foreach ($nameFields as $field) {
                    if (!$userManager->validateArabicName($input[$field])) {
                        $errors[] = "الحقل {$field} يجب أن يحتوي على أحرف عربية فقط";
                    }
                }

                // Validate age
                if (!$userManager->validateAge($input['birth_date'])) {
                    $errors[] = ERROR_MESSAGES['invalid_age'];
                }

                // Validate phone numbers
                $phoneFields = ['personal_phone', 'father_phone', 'mother_phone'];
                foreach ($phoneFields as $field) {
                    if (!$userManager->validatePhone($input[$field])) {
                        $errors[] = ERROR_MESSAGES['invalid_phone'];
                    }
                }
            }

            if ($errors) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => implode('<br>', $errors)
                ]);
                exit;
            }

            // Create user
            $userId = $userManager->createUser($input);

            if ($userId) {
                // Send welcome notification
                $notificationHelper->sendWelcomeNotification($userId);

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => SUCCESS_MESSAGES['registration_success']
                ]);
                exit;
            } else {
                throw new Exception('فشل في إنشاء الحساب');
            }

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الحساب: ' . $e->getMessage()
            ]);
            exit;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="إنشاء حساب جديد في منصة سلسلة الدكتور لتعليم اللغة العربية">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/enhanced-ui.css">
    <link rel="stylesheet" href="css/loading-system.css">
    <link rel="stylesheet" href="css/notifications.css">

</head>
<body>
    <!-- Loading Screen will be created by JavaScript -->

    <!-- Split Screen Container -->
    <div class="split-container">
        <!-- Left Side - Registration Form -->
        <div class="split-left">
            <div class="form-container">
                <h1 class="form-title">إنشاء حساب جديد</h1>
                <p class="form-subtitle">انضم إلى منصة سلسلة الدكتور وابدأ رحلتك التعليمية</p>

                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step active" data-step="1">1</div>
                    <div class="step" data-step="2">2</div>
                    <div class="step" data-step="3">3</div>
                    <div class="step" data-step="4">4</div>
                    <div class="step" data-step="5">5</div>
                </div>

                <form id="registrationForm">
                    <!-- Step 1: Account Credentials -->
                    <div id="step-1" class="form-step active">
                        <h3 class="mb-20">الخطوة 1: بيانات الحساب</h3>

                        <div class="form-group">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                class="form-input"
                                required
                                placeholder="أدخل اسم المستخدم"
                            >
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                class="form-input"
                                required
                                placeholder="أدخل البريد الإلكتروني"
                            >
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                class="form-input"
                                required
                                placeholder="أدخل كلمة المرور"
                            >
                            <div class="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <div class="strength-text">قوة كلمة المرور</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password_confirm" class="form-label">تأكيد كلمة المرور</label>
                            <input
                                type="password"
                                id="password_confirm"
                                name="password_confirm"
                                class="form-input"
                                required
                                placeholder="أعد إدخال كلمة المرور"
                            >
                        </div>
                    </div>

                    <!-- Step 2: Personal Information -->
                    <div id="step-2" class="form-step">
                        <h3 class="mb-20">الخطوة 2: البيانات الشخصية</h3>

                        <div class="form-group">
                            <label for="first_name" class="form-label">الاسم الأول</label>
                            <input
                                type="text"
                                id="first_name"
                                name="first_name"
                                class="form-input"
                                required
                                placeholder="أدخل الاسم الأول"
                            >
                        </div>

                        <div class="form-group">
                            <label for="second_name" class="form-label">الاسم الثاني</label>
                            <input
                                type="text"
                                id="second_name"
                                name="second_name"
                                class="form-input"
                                required
                                placeholder="أدخل الاسم الثاني"
                            >
                        </div>

                        <div class="form-group">
                            <label for="third_name" class="form-label">الاسم الثالث</label>
                            <input
                                type="text"
                                id="third_name"
                                name="third_name"
                                class="form-input"
                                required
                                placeholder="أدخل الاسم الثالث"
                            >
                        </div>

                        <div class="form-group">
                            <label for="fourth_name" class="form-label">الاسم الرابع</label>
                            <input
                                type="text"
                                id="fourth_name"
                                name="fourth_name"
                                class="form-input"
                                required
                                placeholder="أدخل الاسم الرابع"
                            >
                        </div>

                        <div class="preview-section">
                            <div class="preview-title">معاينة الاسم الكامل:</div>
                            <div id="name_preview" class="preview-content">لم يتم إدخال الاسم بعد</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">الجنس</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="male" name="gender" value="male" required>
                                    <label for="male">ذكر</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="female" name="gender" value="female" required>
                                    <label for="female">أنثى</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                            <input
                                type="date"
                                id="birth_date"
                                name="birth_date"
                                class="form-input"
                                required
                            >
                        </div>
                    </div>

                    <!-- Step 3: Contact Information -->
                    <div id="step-3" class="form-step">
                        <h3 class="mb-20">الخطوة 3: بيانات الاتصال</h3>

                        <div class="form-group">
                            <label for="personal_phone" class="form-label">رقم الهاتف الشخصي</label>
                            <input
                                type="tel"
                                id="personal_phone"
                                name="personal_phone"
                                class="form-input"
                                required
                                placeholder="01xxxxxxxxx"
                                maxlength="11"
                            >
                        </div>

                        <div class="form-group">
                            <label for="father_phone" class="form-label">رقم هاتف الأب</label>
                            <input
                                type="tel"
                                id="father_phone"
                                name="father_phone"
                                class="form-input"
                                required
                                placeholder="01xxxxxxxxx"
                                maxlength="11"
                            >
                        </div>

                        <div class="form-group">
                            <label for="mother_phone" class="form-label">رقم هاتف الأم</label>
                            <input
                                type="tel"
                                id="mother_phone"
                                name="mother_phone"
                                class="form-input"
                                required
                                placeholder="01xxxxxxxxx"
                                maxlength="11"
                            >
                        </div>

                        <div class="alert alert-info">
                            <strong>ملاحظة:</strong> يجب أن تبدأ أرقام الهواتف بـ 010 أو 011 أو 012 أو 015 وتكون مكونة من 11 رقم
                        </div>
                    </div>

                    <!-- Step 4: Educational Information -->
                    <div id="step-4" class="form-step">
                        <h3 class="mb-20">الخطوة 4: البيانات التعليمية</h3>

                        <div class="form-group">
                            <label for="education_level" class="form-label">المرحلة التعليمية</label>
                            <select id="education_level" name="education_level" class="form-select" required>
                                <option value="">اختر المرحلة التعليمية</option>
                                <option value="primary">ابتدائي</option>
                                <option value="preparatory">إعدادي</option>
                                <option value="secondary">ثانوي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="education_type" class="form-label">نوع التعليم</label>
                            <select id="education_type" name="education_type" class="form-select" required>
                                <option value="">اختر نوع التعليم</option>
                                <option value="azhari">أزهري</option>
                                <option value="general">عام</option>
                            </select>
                        </div>

                        <div id="specialization_container" class="form-group" style="display: none;">
                            <label class="form-label">التخصص (للثانوي فقط)</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="scientific" name="specialization" value="scientific">
                                    <label for="scientific">علمي</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="literary" name="specialization" value="literary">
                                    <label for="literary">أدبي</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="grade" class="form-label">الصف</label>
                            <select id="grade" name="grade" class="form-select" required>
                                <option value="">اختر الصف</option>
                            </select>
                        </div>

                        <div class="preview-section">
                            <div class="preview-title">معاينة الصف المختار:</div>
                            <div id="education_preview" class="preview-content">لم يتم اختيار الصف بعد</div>
                        </div>
                    </div>

                    <!-- Step 5: Terms and Confirmation -->
                    <div id="step-5" class="form-step">
                        <h3 class="mb-20">الخطوة 5: الشروط والأحكام</h3>

                        <div class="alert alert-info">
                            <strong>مراجعة البيانات:</strong><br>
                            يرجى مراجعة جميع البيانات المدخلة قبل إنشاء الحساب. تأكد من صحة جميع المعلومات.
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="terms_agreement" name="terms_agreement" required>
                                    <label for="terms_agreement">
                                        أوافق على <a href="#" style="color: #4682B4;">الشروط والأحكام</a>
                                        و <a href="#" style="color: #4682B4;">سياسة الخصوصية</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="newsletter" name="newsletter">
                                    <label for="newsletter">أرغب في تلقي النشرة الإخبارية والعروض الخاصة</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Navigation -->
                    <div class="form-navigation">
                        <button type="button" id="prevBtn" class="btn btn-secondary btn-nav" onclick="prevStep()" style="display: none;">
                            السابق
                        </button>
                        <button type="button" id="nextBtn" class="btn btn-primary btn-nav" onclick="nextStep()">
                            التالي
                        </button>
                        <button type="button" id="submitBtn" class="btn btn-success btn-nav" onclick="submitRegistrationForm()" style="display: none;">
                            إنشاء الحساب
                        </button>
                    </div>
                </form>

                <div class="text-center mt-20">
                    <p>لديك حساب بالفعل؟ <a href="login.php" style="color: #4682B4; text-decoration: none; font-weight: bold;">تسجيل الدخول</a></p>
                </div>
            </div>
        </div>

        <!-- Right Side - Platform Features -->
        <div class="split-right">
            <div class="platform-features">
                <img src="img/logo-b.png" alt="شعار سلسلة الدكتور" class="platform-logo">
                <h1 class="platform-title">سلسلة الدكتور</h1>
                <p class="platform-subtitle">منصة تعليم اللغة العربية مع أفضل المعلمين<br>مستر محمد عبدالله</p>

                <!-- Registration Steps -->
                <div class="registration-steps">
                    <div class="steps-title">خطوات التسجيل</div>
                    <ul class="steps-list" id="steps-progress">
                        <li data-step="1" class="active">إدخال بيانات الحساب</li>
                        <li data-step="2">البيانات الشخصية والاسم</li>
                        <li data-step="3">أرقام الهواتف للتواصل</li>
                        <li data-step="4">المعلومات التعليمية</li>
                        <li data-step="5">الموافقة على الشروط</li>
                    </ul>
                </div>
        
            </div>
        </div>
    </div>

    <!-- Welcome Modal -->
    <div id="welcomeModal" class="modal">
        <div class="modal-content">
            <h2 class="modal-title">مرحباً بك في سلسلة الدكتور</h2>
            <p class="modal-text">
                منصة تعليمية متميزة لتعلم اللغة العربية مع أفضل المعلمين.<br>
                ابدأ بإنشاء حساب جديد للانضمام إلى عائلة سلسلة الدكتور!
            </p>
            <div class="modal-buttons">
                <button class="btn btn-primary" onclick="hideModal('welcomeModal')">ابدأ التسجيل</button>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="modal">
        <div class="modal-content">
            <h2 class="modal-title">تأكيد إنشاء الحساب</h2>
            <p class="modal-text">
                هل أنت متأكد من رغبتك في إنشاء الحساب بالبيانات المدخلة؟<br>
                تأكد من صحة جميع المعلومات قبل المتابعة.
            </p>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="hideModal('confirmationModal')">مراجعة البيانات</button>
                <button class="btn btn-success" onclick="confirmRegistration()">تأكيد إنشاء الحساب</button>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="modal">
        <div class="modal-content">
            <h2 class="modal-title">تم إنشاء الحساب بنجاح!</h2>
            <p class="modal-text">
                مرحباً بك في منصة سلسلة الدكتور. تم إنشاء حسابك بنجاح.<br>
                يمكنك الآن تسجيل الدخول والبدء في رحلتك التعليمية.
            </p>
            <div class="modal-buttons">
                <button class="btn btn-primary" onclick="window.location.href='login.php'">تسجيل الدخول</button>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div id="errorModal" class="modal">
        <div class="modal-content">
            <h2 class="modal-title">خطأ في إنشاء الحساب</h2>
            <p class="modal-text">
                حدث خطأ أثناء إنشاء الحساب. يرجى مراجعة البيانات والمحاولة مرة أخرى.
            </p>
            <div class="modal-buttons">
                <button class="btn btn-primary" onclick="hideModal('errorModal')">حسناً</button>
            </div>
        </div>
    </div>

    <script src="js/notifications.js"></script>
    <script src="js/script.js"></script>
    <script src="js/enhanced-ui.js"></script>
    <script src="js/loading-system.js"></script>
</body>
</html>