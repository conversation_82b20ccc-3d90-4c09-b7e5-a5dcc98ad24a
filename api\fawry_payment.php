<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مسموح']);
    exit;
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['plan_id']) || !isset($input['amount'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات مفقودة']);
    exit;
}

$user_id = $_SESSION['user_id'];
$plan_id = intval($input['plan_id']);
$amount = floatval($input['amount']);

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user and plan details
    $stmt = $db->prepare("SELECT u.*, sp.* FROM users u, subscription_plans sp WHERE u.id = ? AND sp.id = ?");
    $stmt->execute([$user_id, $plan_id]);
    $data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$data) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        exit;
    }
    
    // Check if user has active subscription
    $stmt = $db->prepare("SELECT subscription_status, subscription_end_date FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_subscription = $stmt->fetch(PDO::FETCH_ASSOC);

    $has_active_subscription = $user_subscription &&
                              $user_subscription['subscription_status'] === 'active' &&
                              $user_subscription['subscription_end_date'] &&
                              strtotime($user_subscription['subscription_end_date']) > time();

    if ($has_active_subscription) {
        echo json_encode([
            'success' => false,
            'message' => 'لديك اشتراك نشط بالفعل. لا يمكن الاشتراك في خطة جديدة حتى انتهاء الاشتراك الحالي أو إلغاؤه.'
        ]);
        exit;
    }

    // Check if Fawry is enabled
    if (!PAYMENT_GATEWAYS['fawry']['enabled']) {
        echo json_encode(['success' => false, 'message' => 'فوري غير متاح حالياً']);
        exit;
    }
    
    // Generate unique reference number
    $reference_number = 'SUB_' . $user_id . '_' . time();
    
    // Create subscription record
    $end_date = date('Y-m-d H:i:s', strtotime('+' . $data['duration_days'] . ' days'));
    
    $stmt = $db->prepare("INSERT INTO user_subscriptions (user_id, plan_id, payment_method, payment_status, amount_paid, start_date, end_date) VALUES (?, ?, 'fawry', 'pending', ?, NOW(), ?)");
    $stmt->execute([$user_id, $plan_id, $amount, $end_date]);
    $subscription_id = $db->lastInsertId();
    
    // Create payment record
    $stmt = $db->prepare("INSERT INTO payments (subscription_id, user_id, payment_method, payment_gateway, transaction_id, amount, currency, status) VALUES (?, ?, 'fawry', 'fawry', ?, ?, 'EGP', 'pending')");
    $stmt->execute([$subscription_id, $user_id, $reference_number, $amount]);
    
    // Prepare Fawry payment request
    $fawry_config = PAYMENT_GATEWAYS['fawry'];
    
    $payment_data = [
        'merchantCode' => $fawry_config['merchant_code'],
        'merchantRefNum' => $reference_number,
        'customerMobile' => $data['phone'],
        'customerEmail' => $data['email'],
        'customerName' => $data['full_name'],
        'paymentAmount' => $amount,
        'currencyCode' => 'EGP',
        'description' => 'اشتراك ' . $data['name'] . ' - ' . SITE_NAME,
        'chargeItems' => [
            [
                'itemId' => $plan_id,
                'description' => $data['name'],
                'price' => $amount,
                'quantity' => 1
            ]
        ],
        'returnUrl' => $fawry_config['return_url'] . '?ref=' . $reference_number,
        'authCaptureModePayment' => false
    ];
    
    // Generate signature
    $signature_string = $fawry_config['merchant_code'] . $reference_number . $data['phone'] . $amount . $fawry_config['security_key'];
    $signature = hash('sha256', $signature_string);
    $payment_data['signature'] = $signature;
    
    // Send request to Fawry
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $fawry_config['base_url'] . '/ECommerceWeb/Fawry/payments/charge');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        $fawry_response = json_decode($response, true);
        
        if ($fawry_response && isset($fawry_response['statusCode']) && $fawry_response['statusCode'] == 200) {
            // Update payment with Fawry response
            $stmt = $db->prepare("UPDATE payments SET gateway_response = ? WHERE transaction_id = ?");
            $stmt->execute([json_encode($fawry_response), $reference_number]);
            
            echo json_encode([
                'success' => true,
                'payment_url' => $fawry_response['nextAction']['redirectUrl'] ?? null,
                'reference_number' => $reference_number,
                'message' => 'تم إنشاء طلب الدفع بنجاح'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'خطأ من فوري: ' . ($fawry_response['statusDescription'] ?? 'خطأ غير معروف')
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في الاتصال بفوري'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()]);
}
?>
