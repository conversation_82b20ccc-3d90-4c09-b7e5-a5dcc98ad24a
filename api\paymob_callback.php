<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/PaymobGateway.php';

// Log callback for debugging
error_log("Paymob callback received: " . file_get_contents('php://input'));

// Get callback data
$callbackData = json_decode(file_get_contents('php://input'), true);

// If JSON decode fails, try to get POST data
if (!$callbackData) {
    $callbackData = $_POST;
}

// Log the callback data
error_log("Paymob callback data: " . print_r($callbackData, true));

try {
    $paymobGateway = new PaymobGateway();
    $result = $paymobGateway->handleCallback($callbackData);
    
    if ($result) {
        http_response_code(200);
        echo "OK";
    } else {
        http_response_code(400);
        echo "FAILED";
    }
    
} catch (Exception $e) {
    error_log("Paymob callback error: " . $e->getMessage());
    http_response_code(500);
    echo "ERROR";
}
?>
