<?php
/**
 * <PERSON><PERSON><PERSON> to update remaining API calls in course_content.php
 */

$file = 'page/course_content.php';
$content = file_get_contents($file);
$originalContent = $content;

// List of all API endpoints that need to be updated
$apiCalls = [
    'get_course_content.php',
    'get_completion_status.php',
    'get_week_exercises.php',
    'get_weekly_test_questions.php',
    'submit_exercise.php',
    'submit_multi_exercise.php',
    'submit_exam.php',
    'submit_weekly_test.php',
    'reset_exercises.php',
    'reset_exam.php',
    'reset_weekly_test.php',
    'get_exercise_results.php',
    'get_exam.php',
    'get_weekly_test.php',
    'update_video_progress.php',
    'save_course_completion_note.php',
    'check_course_completion.php'
];

$updated = false;

foreach ($apiCalls as $api) {
    // Update fetch calls with template literals
    $pattern = '/fetch\(`\<\?php echo SITE_URL; \?\>\/api\/' . preg_quote($api, '/') . '/';
    $replacement = 'fetch(`../api/' . $api;
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, $replacement, $content);
        $updated = true;
        echo "Updated fetch call for $api\n";
    }
    
    // Update regular string calls
    $pattern = '/\'\<\?php echo SITE_URL; \?\>\/api\/' . preg_quote($api, '/') . '/';
    $replacement = '\'../api/' . $api;
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, $replacement, $content);
        $updated = true;
        echo "Updated string call for $api\n";
    }
}

if ($updated) {
    file_put_contents($file, $content);
    echo "\n✓ Updated API calls in $file\n";
} else {
    echo "• No API calls needed updating in $file\n";
}

echo "Done!\n";
?>
