<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/FawryGateway.php';

// Log callback for debugging
error_log("Fawry callback received: " . file_get_contents('php://input'));

// Get callback data
$callbackData = json_decode(file_get_contents('php://input'), true);

// If JSON decode fails, try to get POST data
if (!$callbackData) {
    $callbackData = $_POST;
}

// Log the callback data
error_log("Fawry callback data: " . print_r($callbackData, true));

try {
    $fawryGateway = new FawryGateway();
    $result = $fawryGateway->handleCallback($callbackData);
    
    if ($result) {
        http_response_code(200);
        echo "OK";
    } else {
        http_response_code(400);
        echo "FAILED";
    }
    
} catch (Exception $e) {
    error_log("Fawry callback error: " . $e->getMessage());
    http_response_code(500);
    echo "ERROR";
}
?>
