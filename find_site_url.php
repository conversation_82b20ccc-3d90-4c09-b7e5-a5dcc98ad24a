<?php
/**
 * <PERSON>ript to find all remaining SITE_URL references
 */

function findSiteUrlReferences($dir = '.', $level = 0) {
    $results = [];
    $items = scandir($dir);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $fullPath = $dir . DIRECTORY_SEPARATOR . $item;
        
        if (is_dir($fullPath)) {
            // Skip certain directories
            if (in_array($item, ['uploads', 'DB', '.git', 'node_modules']) || $level > 3) {
                continue;
            }
            $results = array_merge($results, findSiteUrlReferences($fullPath, $level + 1));
        } elseif (pathinfo($item, PATHINFO_EXTENSION) === 'php') {
            $content = file_get_contents($fullPath);
            if (strpos($content, 'SITE_URL') !== false) {
                // Find specific lines with SITE_URL
                $lines = explode("\n", $content);
                foreach ($lines as $lineNum => $line) {
                    if (strpos($line, 'SITE_URL') !== false) {
                        $results[] = [
                            'file' => str_replace('./', '', $fullPath),
                            'line' => $lineNum + 1,
                            'content' => trim($line)
                        ];
                    }
                }
            }
        }
    }
    
    return $results;
}

echo "=== Finding all SITE_URL references ===\n\n";

$references = findSiteUrlReferences();

if (empty($references)) {
    echo "No SITE_URL references found!\n";
} else {
    echo "Found " . count($references) . " SITE_URL references:\n\n";
    
    $currentFile = '';
    foreach ($references as $ref) {
        if ($ref['file'] !== $currentFile) {
            $currentFile = $ref['file'];
            echo "\n📁 {$ref['file']}:\n";
        }
        echo "   Line {$ref['line']}: {$ref['content']}\n";
    }
}

echo "\n=== Analysis ===\n";
echo "Header redirects (should keep SITE_URL): " . count(array_filter($references, function($ref) {
    return strpos($ref['content'], 'header(') !== false;
})) . "\n";

echo "CSS/JS/Image links (should be relative): " . count(array_filter($references, function($ref) {
    return strpos($ref['content'], 'href=') !== false || strpos($ref['content'], 'src=') !== false;
})) . "\n";

echo "JavaScript assignments (should be dynamic): " . count(array_filter($references, function($ref) {
    return strpos($ref['content'], 'window.SITE_URL') !== false;
})) . "\n";

echo "Config definitions (should remain): " . count(array_filter($references, function($ref) {
    return strpos($ref['content'], "define('SITE_URL'") !== false;
})) . "\n";
?>
