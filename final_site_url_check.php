<?php
/**
 * Final check for SITE_URL references
 */

function checkFile($file) {
    if (!file_exists($file)) return [];
    
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    $results = [];
    
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, 'SITE_URL') !== false) {
            $type = 'other';
            if (strpos($line, 'header(') !== false) {
                $type = 'header_redirect';
            } elseif (strpos($line, 'window.SITE_URL') !== false) {
                $type = 'js_assignment';
            } elseif (strpos($line, "define('SITE_URL'") !== false) {
                $type = 'config_definition';
            } elseif (strpos($line, 'href=') !== false || strpos($line, 'src=') !== false) {
                $type = 'resource_link';
            } elseif (strpos($line, 'fetch(') !== false || strpos($line, '/api/') !== false) {
                $type = 'api_call';
            }
            
            $results[] = [
                'line' => $lineNum + 1,
                'content' => trim($line),
                'type' => $type
            ];
        }
    }
    
    return $results;
}

$filesToCheck = [
    'page/course_content.php',
    'page/summary_viewer.php',
    'page/dashboard.php',
    'admin/exams.php',
    'admin/honor_board.php',
    'admin/news.php',
    'admin/lesson_content.php',
    'includes/subscription_notification.php',
    'config/config.php'
];

echo "=== Final SITE_URL Check ===\n\n";

$totalIssues = 0;
$resourceLinks = 0;
$apiCalls = 0;

foreach ($filesToCheck as $file) {
    $results = checkFile($file);
    if (!empty($results)) {
        echo "📁 $file:\n";
        foreach ($results as $result) {
            echo "   Line {$result['line']} ({$result['type']}): {$result['content']}\n";
            $totalIssues++;
            if ($result['type'] === 'resource_link') $resourceLinks++;
            if ($result['type'] === 'api_call') $apiCalls++;
        }
        echo "\n";
    }
}

echo "=== Summary ===\n";
echo "Total remaining issues: $totalIssues\n";
echo "Resource links that need fixing: $resourceLinks\n";
echo "API calls that need fixing: $apiCalls\n";

if ($resourceLinks > 0 || $apiCalls > 0) {
    echo "\n⚠️  Still need to fix resource links and API calls!\n";
} else {
    echo "\n✅ All resource links and API calls have been updated!\n";
}
?>
