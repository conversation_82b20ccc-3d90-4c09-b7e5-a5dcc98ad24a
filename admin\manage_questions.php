<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_exam_questions') {
        $examId = $_POST['exam_id'];
        $questions = $_POST['questions'] ?? [];
        
        try {
            $db->beginTransaction();
            
            // Clear existing questions
            $stmt = $db->prepare("DELETE FROM course_exam_questions WHERE exam_id = ?");
            $stmt->execute([$examId]);
            
            // Add new questions
            foreach ($questions as $question) {
                if (empty($question['text']) || empty($question['type'])) continue;
                
                $options = null;
                if ($question['type'] === 'multiple_choice' && !empty($question['options'])) {
                    $options = json_encode(array_filter($question['options']));
                }
                
                $stmt = $db->prepare("
                    INSERT INTO course_exam_questions (exam_id, question_text, question_type, options, correct_answer, explanation, points)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $examId,
                    $question['text'],
                    $question['type'],
                    $options,
                    $question['correct_answer'],
                    $question['explanation'] ?? '',
                    $question['points'] ?? 1
                ]);
            }
            
            $db->commit();
            $success = "تم إضافة أسئلة الامتحان بنجاح";
            
        } catch (Exception $e) {
            $db->rollBack();
            $error = "خطأ في إضافة الأسئلة: " . $e->getMessage();
        }
    }
    
    if ($action === 'add_test_questions') {
        $testId = $_POST['test_id'];
        $questions = $_POST['questions'] ?? [];
        
        try {
            $db->beginTransaction();
            
            // Clear existing questions
            $stmt = $db->prepare("DELETE FROM course_weekly_test_questions WHERE test_id = ?");
            $stmt->execute([$testId]);
            
            // Add new questions
            foreach ($questions as $question) {
                if (empty($question['text']) || empty($question['type'])) continue;
                
                $options = null;
                if ($question['type'] === 'multiple_choice' && !empty($question['options'])) {
                    $options = json_encode(array_filter($question['options']));
                }
                
                $stmt = $db->prepare("
                    INSERT INTO course_weekly_test_questions (test_id, question_text, question_type, options, correct_answer, explanation, points)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $testId,
                    $question['text'],
                    $question['type'],
                    $options,
                    $question['correct_answer'],
                    $question['explanation'] ?? '',
                    $question['points'] ?? 1
                ]);
            }
            
            $db->commit();
            $success = "تم إضافة أسئلة الاختبار بنجاح";
            
        } catch (Exception $e) {
            $db->rollBack();
            $error = "خطأ في إضافة الأسئلة: " . $e->getMessage();
        }
    }
}

// Get courses for selection
$stmt = $db->query("SELECT id, title FROM courses ORDER BY title");
$courses = $stmt->fetchAll();

// Get selected course data
$selectedCourse = $_GET['course_id'] ?? '';
$exams = [];
$weeklyTests = [];

if ($selectedCourse) {
    // Get exams
    $stmt = $db->prepare("SELECT id, title, week_number FROM course_exams WHERE course_id = ? ORDER BY week_number, title");
    $stmt->execute([$selectedCourse]);
    $exams = $stmt->fetchAll();
    
    // Get weekly tests
    $stmt = $db->prepare("SELECT id, title, week_number FROM course_weekly_tests WHERE course_id = ? ORDER BY week_number, title");
    $stmt->execute([$selectedCourse]);
    $weeklyTests = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأسئلة - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">

            <div class="content-header">
                <h1>إدارة أسئلة الامتحانات والاختبارات</h1>
                <p>إضافة وتعديل أسئلة الامتحانات والاختبارات الأسبوعية</p>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <span class="alert-icon">✅</span>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <span class="alert-icon">❌</span>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <!-- Course Selection -->
            <div class="content-card">
                <div class="card-header">
                    <h2>اختيار الكورس</h2>
                </div>
                <form method="GET" class="course-selection">
                    <div class="form-group">
                        <label for="course_id">اختر الكورس:</label>
                        <select name="course_id" id="course_id" class="form-control" onchange="this.form.submit()">
                            <option value="">-- اختر كورس --</option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" <?php echo $selectedCourse == $course['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </form>
            </div>
            
            <?php if ($selectedCourse): ?>
                <!-- Exams Section -->
                <div class="content-card">
                    <div class="card-header">
                        <h2>إدارة أسئلة الامتحانات</h2>
                    </div>
                    
                    <?php if (!empty($exams)): ?>
                        <div class="exams-grid">
                            <?php foreach ($exams as $exam): ?>
                                <div class="exam-card">
                                    <div class="exam-info">
                                        <h4><?php echo htmlspecialchars($exam['title']); ?></h4>
                                        <p>الأسبوع <?php echo $exam['week_number']; ?></p>
                                    </div>
                                    <div class="exam-actions">
                                        <button class="btn btn-primary" onclick="openModal('addModal')" onclick="manageExamQuestions(<?php echo $exam['id']; ?>, '<?php echo htmlspecialchars($exam['title']); ?>')">
                                            <span class="btn-icon">📝</span>
                                            إدارة الأسئلة
                                        </button>
                                        <button class="btn btn-secondary" onclick="previewExam(<?php echo $exam['id']; ?>)">
                                            <span class="btn-icon">👁️</span>
                                            معاينة
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-icon">📋</div>
                            <p>لا توجد امتحانات لهذا الكورس</p>
                            <a href="course_content.php" class="btn btn-primary" onclick="openModal('addModal')">إضافة امتحان جديد</a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Weekly Tests Section -->
                <div class="content-card">
                    <div class="card-header">
                        <h2>إدارة أسئلة الاختبارات الأسبوعية</h2>
                    </div>
                    
                    <?php if (!empty($weeklyTests)): ?>
                        <div class="tests-grid">
                            <?php foreach ($weeklyTests as $test): ?>
                                <div class="test-card">
                                    <div class="test-info">
                                        <h4><?php echo htmlspecialchars($test['title']); ?></h4>
                                        <p>الأسبوع <?php echo $test['week_number']; ?></p>
                                    </div>
                                    <div class="test-actions">
                                        <button class="btn btn-primary" onclick="openModal('addModal')" onclick="manageTestQuestions(<?php echo $test['id']; ?>, '<?php echo htmlspecialchars($test['title']); ?>')">
                                            <span class="btn-icon">📝</span>
                                            إدارة الأسئلة
                                        </button>
                                        <button class="btn btn-secondary" onclick="previewTest(<?php echo $test['id']; ?>)">
                                            <span class="btn-icon">👁️</span>
                                            معاينة
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-icon">🧪</div>
                            <p>لا توجد اختبارات أسبوعية لهذا الكورس</p>
                            <a href="course_content.php" class="btn btn-primary" onclick="openModal('addModal')">إضافة اختبار جديد</a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Questions Management Modal -->
    <div id="questionsModal" class="modal" style="display: none;">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3 id="modalTitle">إدارة الأسئلة</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="questionsForm" method="POST">
                    <input type="hidden" name="action" id="formAction">
                    <input type="hidden" name="exam_id" id="examId">
                    <input type="hidden" name="test_id" id="testId">
                    
                    <div class="questions-info">
                        <div class="info-card">
                            <span class="info-icon">💡</span>
                            <span class="info-text">يمكنك إضافة أسئلة متعددة. استخدم الأزرار لإضافة أو حذف الأسئلة.</span>
                        </div>
                    </div>
                    
                    <div id="questionsContainer">
                        <!-- Questions will be added here -->
                    </div>
                    
                    <div class="questions-actions">
                        <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="addQuestion()">
                            <span class="btn-icon">➕</span>
                            إضافة سؤال جديد
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearAllQuestions()">
                            <span class="btn-icon">🗑️</span>
                            مسح جميع الأسئلة
                        </button>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="submit" class="btn btn-success">
                            <span class="btn-icon">💾</span>
                            حفظ الأسئلة
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <style>
        .exams-grid, .tests-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .exam-card, .test-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .exam-card:hover, .test-card:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
            transform: translateY(-2px);
        }
        
        .exam-info h4, .test-info h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .exam-actions, .test-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .large-modal {
            max-width: 900px;
            width: 90%;
        }
        
        .questions-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px dashed #dee2e6;
        }
        
        .btn-icon {
            margin-left: 8px;
        }

        .question-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
        }

        .question-item:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
            transform: translateY(-2px);
        }

        .enhanced-question {
            border-left: 4px solid #007bff;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .question-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .question-number {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
        }

        .question-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .question-actions {
            display: flex;
            gap: 10px;
        }

        .question-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .enhanced-textarea {
            min-height: 80px;
            resize: vertical;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .col-md-6 {
            flex: 0 0 48%;
        }

        .true-false-options, .multiple-choice-options {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-top: 15px;
        }

        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 10px 15px;
            border-radius: 8px;
            background: white;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .radio-option:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }

        .radio-option input[type="radio"]:checked + .radio-custom + span {
            color: #007bff;
            font-weight: 600;
        }

        .options-list {
            margin-top: 15px;
        }

        .option-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .option-item input[type="text"] {
            flex: 1;
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 8px 12px;
        }

        .correct-indicator {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .option-item input[type="radio"]:checked ~ .correct-indicator {
            opacity: 1;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 2px solid #e9ecef;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 25px;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }
    </style>
    
    <script src="../js/admin.js"></script>
    <script>
        let questionCount = 0;
        
        function manageExamQuestions(examId, examTitle) {
            document.getElementById('modalTitle').textContent = `إدارة أسئلة: ${examTitle}`;
            document.getElementById('formAction').value = 'add_exam_questions';
            document.getElementById('examId').value = examId;
            document.getElementById('testId').value = '';
            document.getElementById('questionsContainer').innerHTML = '';
            questionCount = 0;
            document.getElementById('questionsModal').style.display = 'flex';
            
            // Load existing questions
            loadExistingQuestions('exam', examId);
        }
        
        function manageTestQuestions(testId, testTitle) {
            document.getElementById('modalTitle').textContent = `إدارة أسئلة: ${testTitle}`;
            document.getElementById('formAction').value = 'add_test_questions';
            document.getElementById('testId').value = testId;
            document.getElementById('examId').value = '';
            document.getElementById('questionsContainer').innerHTML = '';
            questionCount = 0;
            document.getElementById('questionsModal').style.display = 'flex';
            
            // Load existing questions
            loadExistingQuestions('test', testId);
        }
        
        function closeModal() {
            document.getElementById('questionsModal').style.display = 'none';
        }
        
        function addQuestion() {
            questionCount++;
            const container = document.getElementById('questionsContainer');
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-item enhanced-question';
            questionDiv.setAttribute('data-question-id', questionCount);
            questionDiv.innerHTML = `
                <div class="question-header">
                    <div class="question-title">
                        <span class="question-number">السؤال ${questionCount}</span>
                        <span class="question-badge">جديد</span>
                    </div>
                    <div class="question-actions">
                        <button type="button" class="btn btn-secondary btn-sm" onclick="duplicateQuestion(this)">
                            <span class="btn-icon">📋</span>
                            نسخ
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeQuestion(this)">
                            <span class="btn-icon">🗑️</span>
                            حذف
                        </button>
                    </div>
                </div>
                <div class="question-content">
                    <div class="form-group">
                        <label>نص السؤال *</label>
                        <textarea name="questions[${questionCount-1}][text]" required class="form-control enhanced-textarea" rows="3" placeholder="اكتب نص السؤال هنا..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>نوع السؤال *</label>
                            <select name="questions[${questionCount-1}][type]" required class="form-control" onchange="updateQuestionType(this, ${questionCount-1})">
                                <option value="">اختر نوع السؤال</option>
                                <option value="true_false">صح أو خطأ</option>
                                <option value="multiple_choice">اختيار متعدد</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>النقاط *</label>
                            <input type="number" name="questions[${questionCount-1}][points]" required class="form-control" min="0.5" step="0.5" value="1">
                        </div>
                    </div>
                    <div id="options_${questionCount-1}" class="options-container">
                        <!-- Options will be added based on question type -->
                    </div>
                    <div class="form-group">
                        <label>الشرح (اختياري)</label>
                        <textarea name="questions[${questionCount-1}][explanation]" class="form-control" rows="2" placeholder="اكتب شرحاً للإجابة الصحيحة (اختياري)..."></textarea>
                    </div>
                    <input type="hidden" name="questions[${questionCount-1}][correct_answer]">
                </div>
            `;
            container.appendChild(questionDiv);
        }

        function updateQuestionType(select, questionIndex) {
            const optionsContainer = document.getElementById(`options_${questionIndex}`);
            const hiddenInput = document.querySelector(`input[name="questions[${questionIndex}][correct_answer]"]`);

            if (select.value === 'true_false') {
                optionsContainer.innerHTML = `
                    <div class="true-false-options">
                        <label>الإجابة الصحيحة:</label>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="tf_${questionIndex}" value="true" onchange="setCorrectAnswer(${questionIndex}, 'true')">
                                <span class="radio-custom"></span>
                                صح
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="tf_${questionIndex}" value="false" onchange="setCorrectAnswer(${questionIndex}, 'false')">
                                <span class="radio-custom"></span>
                                خطأ
                            </label>
                        </div>
                    </div>
                `;
            } else if (select.value === 'multiple_choice') {
                optionsContainer.innerHTML = `
                    <div class="multiple-choice-options">
                        <label>خيارات الإجابة:</label>
                        <div class="options-list">
                            <div class="option-item">
                                <input type="radio" name="mc_${questionIndex}" value="0" onchange="setCorrectAnswerFromOption(${questionIndex}, 0)">
                                <input type="text" name="questions[${questionIndex}][options][]" placeholder="الخيار الأول" onchange="updateCorrectAnswerFromOption(${questionIndex})">
                                <span class="correct-indicator">صحيح</span>
                            </div>
                            <div class="option-item">
                                <input type="radio" name="mc_${questionIndex}" value="1" onchange="setCorrectAnswerFromOption(${questionIndex}, 1)">
                                <input type="text" name="questions[${questionIndex}][options][]" placeholder="الخيار الثاني" onchange="updateCorrectAnswerFromOption(${questionIndex})">
                                <span class="correct-indicator">صحيح</span>
                            </div>
                            <div class="option-item">
                                <input type="radio" name="mc_${questionIndex}" value="2" onchange="setCorrectAnswerFromOption(${questionIndex}, 2)">
                                <input type="text" name="questions[${questionIndex}][options][]" placeholder="الخيار الثالث" onchange="updateCorrectAnswerFromOption(${questionIndex})">
                                <span class="correct-indicator">صحيح</span>
                            </div>
                            <div class="option-item">
                                <input type="radio" name="mc_${questionIndex}" value="3" onchange="setCorrectAnswerFromOption(${questionIndex}, 3)">
                                <input type="text" name="questions[${questionIndex}][options][]" placeholder="الخيار الرابع" onchange="updateCorrectAnswerFromOption(${questionIndex})">
                                <span class="correct-indicator">صحيح</span>
                            </div>
                        </div>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="addMoreOption(${questionIndex})">إضافة خيار آخر</button>
                    </div>
                `;
            } else {
                optionsContainer.innerHTML = '';
            }
        }

        function setCorrectAnswer(questionIndex, value) {
            const hiddenInput = document.querySelector(`input[name="questions[${questionIndex}][correct_answer]"]`);
            hiddenInput.value = value;
        }

        function setCorrectAnswerFromOption(questionIndex, optionIndex) {
            const optionInput = document.querySelectorAll(`input[name="questions[${questionIndex}][options][]"]`)[optionIndex];
            const hiddenInput = document.querySelector(`input[name="questions[${questionIndex}][correct_answer]"]`);
            hiddenInput.value = optionInput.value;
        }

        function updateCorrectAnswerFromOption(questionIndex) {
            const selectedRadio = document.querySelector(`input[name="mc_${questionIndex}"]:checked`);
            if (selectedRadio) {
                setCorrectAnswerFromOption(questionIndex, selectedRadio.value);
            }
        }

        function removeQuestion(button) {
            customConfirm.show(
                'حذف السؤال',
                'هل أنت متأكد من حذف هذا السؤال؟ لن تتمكن من استرجاعه.'
            ).then(confirmed => {
                if (confirmed) {
                    button.closest('.question-item').remove();
                    updateQuestionNumbers();
                }
            });
        }

        function duplicateQuestion(button) {
            const questionItem = button.closest('.question-item');
            const clone = questionItem.cloneNode(true);
            questionCount++;

            // Update question number and names in clone
            updateQuestionInClone(clone, questionCount);

            // Insert after current question
            questionItem.parentNode.insertBefore(clone, questionItem.nextSibling);
        }

        function updateQuestionNumbers() {
            const questions = document.querySelectorAll('.question-item');
            questions.forEach((question, index) => {
                const questionNumber = question.querySelector('.question-number');
                if (questionNumber) {
                    questionNumber.textContent = `السؤال ${index + 1}`;
                }
            });
        }

        function updateQuestionInClone(element, newIndex) {
            // Update question number display
            const questionNumber = element.querySelector('.question-number');
            if (questionNumber) {
                questionNumber.textContent = `السؤال ${newIndex}`;
            }

            // Update all input names and IDs
            element.querySelectorAll('input, textarea, select').forEach(input => {
                if (input.name) {
                    input.name = input.name.replace(/\[\d+\]/, `[${newIndex-1}]`);
                    input.name = input.name.replace(/_\d+/, `_${newIndex-1}`);
                }
                if (input.id) {
                    input.id = input.id.replace(/_\d+/, `_${newIndex-1}`);
                }
                // Clear values
                if (input.type !== 'hidden') {
                    input.value = '';
                    input.checked = false;
                }
            });

            // Update onclick handlers
            element.querySelectorAll('[onchange]').forEach(el => {
                if (el.getAttribute('onchange')) {
                    el.setAttribute('onchange', el.getAttribute('onchange').replace(/\d+/g, newIndex-1));
                }
            });
        }

        function clearAllQuestions() {
            customConfirm.show(
                'حذف جميع الأسئلة',
                'هل أنت متأكد من حذف جميع الأسئلة؟ لن تتمكن من استرجاعها.'
            ).then(confirmed => {
                if (confirmed) {
                    document.getElementById('questionsContainer').innerHTML = '';
                    questionCount = 0;
                }
            });
        }

        function loadExistingQuestions(type, id) {
            // Load existing questions via AJAX
            const endpoint = type === 'exam' ? 'get_exam_questions.php' : 'get_weekly_test_questions.php';
            const param = type === 'exam' ? 'exam_id' : 'test_id';

            fetch(`../api/${endpoint}?${param}=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.questions) {
                        data.questions.forEach((question, index) => {
                            addQuestion();
                            populateQuestion(index, question);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading questions:', error);
                });
        }

        function populateQuestion(index, questionData) {
            // Populate question with existing data
            const questionText = document.querySelector(`textarea[name="questions[${index}][text]"]`);
            const questionType = document.querySelector(`select[name="questions[${index}][type]"]`);
            const questionPoints = document.querySelector(`input[name="questions[${index}][points]"]`);
            const questionExplanation = document.querySelector(`textarea[name="questions[${index}][explanation]"]`);

            if (questionText) questionText.value = questionData.question_text;
            if (questionType) {
                questionType.value = questionData.question_type;
                updateQuestionType(questionType, index);
            }
            if (questionPoints) questionPoints.value = questionData.points;
            if (questionExplanation) questionExplanation.value = questionData.explanation || '';

            // Set correct answer based on type
            setTimeout(() => {
                if (questionData.question_type === 'true_false') {
                    const correctRadio = document.querySelector(`input[name="tf_${index}"][value="${questionData.correct_answer}"]`);
                    if (correctRadio) {
                        correctRadio.checked = true;
                        setCorrectAnswer(index, questionData.correct_answer);
                    }
                } else if (questionData.question_type === 'multiple_choice' && questionData.options) {
                    const options = questionData.options;
                    const optionInputs = document.querySelectorAll(`input[name="questions[${index}][options][]"]`);

                    options.forEach((option, optionIndex) => {
                        if (optionInputs[optionIndex]) {
                            optionInputs[optionIndex].value = option;
                            if (option === questionData.correct_answer) {
                                const correctRadio = document.querySelector(`input[name="mc_${index}"][value="${optionIndex}"]`);
                                if (correctRadio) {
                                    correctRadio.checked = true;
                                    setCorrectAnswer(index, option);
                                }
                            }
                        }
                    });
                }
            }, 100);
        }
        
        function previewExam(examId) {
            window.open(`../page/course_content.php?preview_exam=${examId}`, '_blank');
        }
        
        function previewTest(testId) {
            window.open(`../page/course_content.php?preview_test=${testId}`, '_blank');
        }
    </script>
    <script src="../js/custom-confirm.js"></script>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
